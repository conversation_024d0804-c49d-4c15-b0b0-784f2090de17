{"rustc": 10895048813736897673, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 2241668132362809309, "path": 15323137584201916984, "deps": [[784494742817713399, "tower_service", false, 17204960891864552244], [1906322745568073236, "pin_project_lite", false, 16743322304577677416], [4121350475192885151, "iri_string", false, 5496464671307676924], [5695049318159433696, "tower", false, 1081687546831969165], [7712452662827335977, "tower_layer", false, 13431615978387648172], [7896293946984509699, "bitflags", false, 6230422252411917668], [9010263965687315507, "http", false, 14257252463992322348], [10629569228670356391, "futures_util", false, 4346013588814070877], [14084095096285906100, "http_body", false, 3864378268463359641], [16066129441945555748, "bytes", false, 17350977207496306942]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-d87f1f6eebce9b8b\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}