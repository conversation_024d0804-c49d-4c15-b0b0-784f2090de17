{"rustc": 10895048813736897673, "features": "[\"default\", \"procmacros\", \"profiling-procmacros\"]", "declared_features": "[\"default\", \"optick\", \"procmacros\", \"profile-with-optick\", \"profile-with-puffin\", \"profile-with-superluminal\", \"profile-with-tracing\", \"profile-with-tracy\", \"profiling-procmacros\", \"puffin\", \"superluminal-perf\", \"tracing\", \"tracy-client\", \"type-check\"]", "target": 1764792426699693407, "profile": 2241668132362809309, "path": 10401182286567227788, "deps": [[8245969239849424265, "profiling_procmacros", false, 4356288542124882885]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\profiling-9fa2e6045beb1aa1\\dep-lib-profiling", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}