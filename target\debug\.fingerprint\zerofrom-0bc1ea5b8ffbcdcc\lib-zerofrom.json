{"rustc": 10895048813736897673, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 2241668132362809309, "path": 189439151611813757, "deps": [[4022439902832367970, "zerofrom_derive", false, 451087594099951088]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerofrom-0bc1ea5b8ffbcdcc\\dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}