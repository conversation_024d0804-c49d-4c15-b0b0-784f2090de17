{"rustc": 10895048813736897673, "features": "[\"__tls\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"stream\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 447021486742529345, "path": 13267138496338096405, "deps": [[40386456601120721, "percent_encoding", false, 13038021202010435517], [95042085696191081, "ipnet", false, 2500641194249529294], [418947936956741439, "h2", false, 3393484591056052655], [778154619793643451, "hyper_util", false, 3850838715423937300], [784494742817713399, "tower_service", false, 17204960891864552244], [1288403060204016458, "tokio_util", false, 14874804934029588508], [1906322745568073236, "pin_project_lite", false, 16743322304577677416], [2054153378684941554, "tower_http", false, 2557306425694684122], [2517136641825875337, "sync_wrapper", false, 10556356496466133624], [2883436298747778685, "rustls_pki_types", false, 5853870991600495825], [3150220818285335163, "url", false, 11604516703839765712], [3722963349756955755, "once_cell", false, 4507777118843522040], [5695049318159433696, "tower", false, 13007193451829419486], [5986029879202738730, "log", false, 4806737539492256673], [7620660491849607393, "futures_core", false, 9411982572910669251], [9010263965687315507, "http", false, 14257252463992322348], [9538054652646069845, "tokio", false, 3840114820292566334], [9689903380558560274, "serde", false, 17690120068839890953], [10229185211513642314, "mime", false, 7519857400203241190], [10629569228670356391, "futures_util", false, 15522031845263862123], [11957360342995674422, "hyper", false, 8941268303499997737], [12186126227181294540, "tokio_native_tls", false, 16103524345668820502], [13077212702700853852, "base64", false, 17996518868991860874], [14084095096285906100, "http_body", false, 3864378268463359641], [14564311161534545801, "encoding_rs", false, 9493478070620826999], [15367738274754116744, "serde_json", false, 2738634569744250569], [16066129441945555748, "bytes", false, 17350977207496306942], [16542808166767769916, "serde_urlencoded", false, 3829771684768242963], [16785601910559813697, "native_tls_crate", false, 17165938428521681089], [16900715236047033623, "http_body_util", false, 16236257450599643841], [18273243456331255970, "hyper_tls", false, 17276870675205466840]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-0f1a314374391434\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}