{"rustc": 10895048813736897673, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17467636112133979524, "path": 14481626784104993609, "deps": [[5103565458935487, "futures_io", false, 17410487619064049484], [1615478164327904835, "pin_utils", false, 681689265770673094], [1811549171721445101, "futures_channel", false, 2336794669831396590], [1906322745568073236, "pin_project_lite", false, 16743322304577677416], [3129130049864710036, "memchr", false, 8665198149556874655], [6955678925937229351, "slab", false, 18222121208775303383], [7013762810557009322, "futures_sink", false, 6859426977571588960], [7620660491849607393, "futures_core", false, 9411982572910669251], [10565019901765856648, "futures_macro", false, 1030626978251781580], [16240732885093539806, "futures_task", false, 1156322232468433041]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-2890bcadb5ba6b71\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}