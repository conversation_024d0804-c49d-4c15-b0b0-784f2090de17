{"rustc": 10895048813736897673, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 16776477491123527738, "deps": [[1988483478007900009, "unicode_ident", false, 14776100938691580130], [3060637413840920116, "proc_macro2", false, 138309430727902746], [17990358020177143287, "quote", false, 16112963820645638977]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-c6d58b2650e38392\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}