{"$message_type":"diagnostic","message":"unused import: `SandboxType`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"core\\src\\execution.rs","byte_start":67,"byte_end":78,"line_start":2,"line_end":2,"column_start":28,"column_end":39,"is_primary":true,"text":[{"text":"use crate::{SandboxPolicy, SandboxType, AskForApproval};","highlight_start":28,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"core\\src\\execution.rs","byte_start":65,"byte_end":78,"line_start":2,"line_end":2,"column_start":26,"column_end":39,"is_primary":true,"text":[{"text":"use crate::{SandboxPolicy, SandboxType, AskForApproval};","highlight_start":26,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `SandboxType`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcore\\src\\execution.rs:2:28\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::{SandboxPolicy, SandboxType, AskForApproval};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `ArienError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"core\\src\\mcp_connection_manager.rs","byte_start":58,"byte_end":68,"line_start":2,"line_end":2,"column_start":28,"column_end":38,"is_primary":true,"text":[{"text":"use arien_common::{Result, ArienError};","highlight_start":28,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"core\\src\\mcp_connection_manager.rs","byte_start":56,"byte_end":68,"line_start":2,"line_end":2,"column_start":26,"column_end":38,"is_primary":true,"text":[{"text":"use arien_common::{Result, ArienError};","highlight_start":26,"highlight_end":38}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"core\\src\\mcp_connection_manager.rs","byte_start":49,"byte_end":50,"line_start":2,"line_end":2,"column_start":19,"column_end":20,"is_primary":true,"text":[{"text":"use arien_common::{Result, ArienError};","highlight_start":19,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"core\\src\\mcp_connection_manager.rs","byte_start":68,"byte_end":69,"line_start":2,"line_end":2,"column_start":38,"column_end":39,"is_primary":true,"text":[{"text":"use arien_common::{Result, ArienError};","highlight_start":38,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `ArienError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcore\\src\\mcp_connection_manager.rs:2:28\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse arien_common::{Result, ArienError};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::path::Path`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"core\\src\\patch.rs","byte_start":108,"byte_end":123,"line_start":3,"line_end":3,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"use std::path::Path;","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"core\\src\\patch.rs","byte_start":104,"byte_end":125,"line_start":3,"line_end":4,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::path::Path;","highlight_start":1,"highlight_end":21},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::path::Path`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcore\\src\\patch.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::path::Path;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `items`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"core\\src\\session.rs","byte_start":10501,"byte_end":10506,"line_start":288,"line_end":288,"column_start":87,"column_end":92,"is_primary":true,"text":[{"text":"    async fn process_with_model(&mut self, submission_id: arien_common::SubmissionId, items: Vec<InputItem>) -> Result<()> {","highlight_start":87,"highlight_end":92}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"core\\src\\session.rs","byte_start":10501,"byte_end":10506,"line_start":288,"line_end":288,"column_start":87,"column_end":92,"is_primary":true,"text":[{"text":"    async fn process_with_model(&mut self, submission_id: arien_common::SubmissionId, items: Vec<InputItem>) -> Result<()> {","highlight_start":87,"highlight_end":92}],"label":null,"suggested_replacement":"_items","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `items`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcore\\src\\session.rs:288:87\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m288\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m arien_common::SubmissionId, items: Vec<InputItem>) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_items`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `tool_name`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"core\\src\\session.rs","byte_start":12601,"byte_end":12610,"line_start":338,"line_end":338,"column_start":126,"column_end":135,"is_primary":true,"text":[{"text":"    async fn execute_tool_call(&mut self, submission_id: arien_common::SubmissionId, tool_call_id: arien_common::ToolCallId, tool_name: String, arguments: HashMap<String, serde_json::Value>) -> Result<()> {","highlight_start":126,"highlight_end":135}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"core\\src\\session.rs","byte_start":12601,"byte_end":12610,"line_start":338,"line_end":338,"column_start":126,"column_end":135,"is_primary":true,"text":[{"text":"    async fn execute_tool_call(&mut self, submission_id: arien_common::SubmissionId, tool_call_id: arien_common::ToolCallId, tool_name: String, arguments: HashMap<String, serde_json::Value>) -> Result<()> {","highlight_start":126,"highlight_end":135}],"label":null,"suggested_replacement":"_tool_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `tool_name`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcore\\src\\session.rs:338:126\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m338\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m arien_common::ToolCallId, tool_name: String, arguments: HashMap<String, serd\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_tool_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `arguments`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"core\\src\\session.rs","byte_start":12620,"byte_end":12629,"line_start":338,"line_end":338,"column_start":145,"column_end":154,"is_primary":true,"text":[{"text":"    async fn execute_tool_call(&mut self, submission_id: arien_common::SubmissionId, tool_call_id: arien_common::ToolCallId, tool_name: String, arguments: HashMap<String, serde_json::Value>) -> Result<()> {","highlight_start":145,"highlight_end":154}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"core\\src\\session.rs","byte_start":12620,"byte_end":12629,"line_start":338,"line_end":338,"column_start":145,"column_end":154,"is_primary":true,"text":[{"text":"    async fn execute_tool_call(&mut self, submission_id: arien_common::SubmissionId, tool_call_id: arien_common::ToolCallId, tool_name: String, arguments: HashMap<String, serde_json::Value>) -> Result<()> {","highlight_start":145,"highlight_end":154}],"label":null,"suggested_replacement":"_arguments","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `arguments`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcore\\src\\session.rs:338:145\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m338\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mCallId, tool_name: String, arguments: HashMap<String, serde_json::Value>) -> \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_arguments`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `working_dir`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"core\\src\\execution.rs","byte_start":2396,"byte_end":2407,"line_start":68,"line_end":68,"column_start":59,"column_end":70,"is_primary":true,"text":[{"text":"    async fn execute_command(&self, command: Vec<String>, working_dir: Option<std::path::PathBuf>) -> Result<ExecutionResult> {","highlight_start":59,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"core\\src\\execution.rs","byte_start":2396,"byte_end":2407,"line_start":68,"line_end":68,"column_start":59,"column_end":70,"is_primary":true,"text":[{"text":"    async fn execute_command(&self, command: Vec<String>, working_dir: Option<std::path::PathBuf>) -> Result<ExecutionResult> {","highlight_start":59,"highlight_end":70}],"label":null,"suggested_replacement":"_working_dir","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `working_dir`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcore\\src\\execution.rs:68:59\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m68\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0melf, command: Vec<String>, working_dir: Option<std::path::PathBuf>) -> Result<\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_working_dir`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `sandbox_policy` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"core\\src\\execution.rs","byte_start":152,"byte_end":167,"line_start":5,"line_end":5,"column_start":12,"column_end":27,"is_primary":false,"text":[{"text":"pub struct CommandExecutor {","highlight_start":12,"highlight_end":27}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"core\\src\\execution.rs","byte_start":174,"byte_end":188,"line_start":6,"line_end":6,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    sandbox_policy: SandboxPolicy,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: field `sandbox_policy` is never read\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcore\\src\\execution.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct CommandExecutor {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    sandbox_policy: SandboxPolicy,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"8 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 8 warnings emitted\u001b[0m\n\n"}
