{"rustc": 10895048813736897673, "features": "[\"threading\"]", "declared_features": "[\"aom-sys\", \"asm\", \"av-metrics\", \"backtrace\", \"bench\", \"binaries\", \"byteorder\", \"capi\", \"cc\", \"channel-api\", \"check_asm\", \"clap\", \"clap_complete\", \"console\", \"crossbeam\", \"dav1d-sys\", \"decode_test\", \"decode_test_dav1d\", \"default\", \"desync_finder\", \"dump_ivf\", \"dump_lookahead_data\", \"fern\", \"git_version\", \"image\", \"ivf\", \"nasm-rs\", \"nom\", \"quick_test\", \"scan_fmt\", \"scenechange\", \"serde\", \"serde-big-array\", \"serialize\", \"signal-hook\", \"signal_support\", \"threading\", \"toml\", \"tracing\", \"tracing-chrome\", \"tracing-subscriber\", \"unstable\", \"wasm\", \"wasm-bindgen\", \"y4m\"]", "target": 12405811532001061035, "profile": 2241668132362809309, "path": 3111014970891453693, "deps": [[1895578031732481377, "profiling", false, 3038023797467201537], [2687729594444538932, "debug_unreachable", false, 2647899805566413350], [2924422107542798392, "libc", false, 14034203357663649923], [3722963349756955755, "once_cell", false, 4507777118843522040], [5157631553186200874, "num_traits", false, 556109256811421111], [5237962722597217121, "simd_helpers", false, 15712101613572785814], [5626665093607998638, "build_script_build", false, 5268718001843291080], [5986029879202738730, "log", false, 4806737539492256673], [7074416887430417773, "av1_grain", false, 10994444405733533528], [8008191657135824715, "thiserror", false, 10923360883378088787], [10411997081178400487, "cfg_if", false, 17451303736681025148], [11063920846464372013, "v_frame", false, 7989543027741629055], [11263754829263059703, "num_derive", false, 4740672213168636671], [12672448913558545127, "noop_proc_macro", false, 4387884620629638612], [13847662864258534762, "arrayvec", false, 7293023699517961101], [14931062873021150766, "itertools", false, 2060124795733802642], [16507960196461048755, "rayon", false, 6215119817573658557], [17605717126308396068, "paste", false, 7770359365849985443], [17706129463675219700, "arg_enum_proc_macro", false, 1665947759010926942], [17933778289016427379, "bitstream_io", false, 10029906894234250657]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rav1e-0bea02d4bae4c3bc\\dep-lib-rav1e", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}