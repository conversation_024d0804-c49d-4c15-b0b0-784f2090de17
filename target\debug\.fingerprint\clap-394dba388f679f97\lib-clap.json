{"rustc": 10895048813736897673, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 7588797288915553443, "path": 14927427181858866332, "deps": [[3019522439560520108, "clap_builder", false, 4356034940650879748], [17056525256108235978, "clap_derive", false, 8693182851898289032]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap-394dba388f679f97\\dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}