{"rustc": 10895048813736897673, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2241668132362809309, "path": 15490535877894721867, "deps": [[555019317135488525, "regex_automata", false, 4333832794060361343], [2779309023524819297, "aho_corasick", false, 9986430062405487713], [3129130049864710036, "memchr", false, 8665198149556874655], [9408802513701742484, "regex_syntax", false, 5381824103611373532]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-c63556f54eb5dd70\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}