{"rustc": 10895048813736897673, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 7588797288915553443, "path": 14657454648580477989, "deps": [[5820056977320921005, "anstream", false, 2111823108165099642], [9394696648929125047, "anstyle", false, 14237368355653297119], [11166530783118767604, "strsim", false, 5603449405455934565], [12553266436076736472, "clap_lex", false, 6245582624533531163]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap_builder-0fd994e557cc1eee\\dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}