{"rustc": 10895048813736897673, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 11202463608144111571, "path": 7291115985175162958, "deps": [[1009387600818341822, "matchers", false, 2500215386277663336], [1017461770342116999, "sharded_slab", false, 12997552923354154516], [3722963349756955755, "once_cell", false, 4507777118843522040], [6048213226671835012, "smallvec", false, 7984417837040358475], [8606274917505247608, "tracing", false, 7118938455684139428], [8614575489689151157, "nu_ansi_term", false, 12428856542964703074], [9451456094439810778, "regex", false, 3793085470722798449], [10806489435541507125, "tracing_log", false, 9761897165990752441], [11033263105862272874, "tracing_core", false, 3900325596113039030], [12427285511609802057, "thread_local", false, 2589090352299370394]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-c065923a6be974e9\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}